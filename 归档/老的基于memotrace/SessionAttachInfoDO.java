package com.pugwoo.weixin_msg.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("SessionAttachInfo")
public class SessionAttachInfoDO {

    @Column(value = "attachId", isKey = true)
    private Integer attachId;

    @Column(value = "entityId")
    private Integer entityId;

    @Column(value = "fromEntityId")
    private Integer fromEntityId;

    @Column(value = "msgType")
    private Integer msgType;

    @Column(value = "msgSubType")
    private Integer msgSubType;

    @Column(value = "msgId")
    private Long msgId;

    @Column(value = "msgTime")
    private Long msgTime;

    @Column(value = "attachSize")
    private Integer attachSize;

    @Column(value = "attachType")
    private Integer attachType;

    @Column(value = "attachPath")
    private String attachPath;

    @Column(value = "Reserved1")
    private String reserved1;

    @Column(value = "Reserved2")
    private String reserved2;

}