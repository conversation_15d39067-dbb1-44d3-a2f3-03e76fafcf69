package com.pugwoo.weixin_msg;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.utils.DBUtils;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class SQLiteTests {

    public static void main(String[] args) {
        String dbPath = "E:/MemoTrace/app/Database/Msg/pugwoo/MultiSearchChatMsg.db";
        DBHelper dbHelper = DBUtils.getDBHelper(dbPath);

        List<Map> rows = dbHelper.getRaw(Map.class, "select * from SessionAttachInfo where attachPath like '%61eacb093e0d5e600bba0095add450fb%'");
        System.out.println(JSON.toJson(rows));
    }

    /**
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG.db: 2055
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG0.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG1.db: 419
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG10.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG11.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG12.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG13.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG14.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG15.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG16.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG17.db: 202
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG18.db: 104
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG2.db: 654
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG3.db: 387
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG4.db: 289
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG5.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG6.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG7.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG8.db: 0
     D:\MemoTrace\app\Database\Msg\pugwoo\MSG9.db: 0
     msgSvrIDs: 2055
     total: 4110

     除了7个MsgSvrID=0（40万分之一），其它都是msgsvrid相同，内容就相同
     */
    @Test
    public void testMsg() {
        List<String> files = DBUtils.listDBFile("E:/User/pugwoo/Msg/Multi", "MSG(\\d*)\\.db");
        int total = 0;
        Map<Long, String> msgSvrIDs = new HashMap<>();
        for (String file : files) {
            DBHelper dbHelper = DBUtils.getDBHelper(file);
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class);
            System.out.println(file + ": " + msgList.size());
            total += msgList.size();
            for (MsgDO msg : msgList) {
                // 判断一下是不是msgsvrid相同，内容就相同：结论，是的
                if (msgSvrIDs.containsKey(msg.getMsgSvrID())) {
                    String oldContent = msgSvrIDs.get(msg.getMsgSvrID());
                    if (!oldContent.equals(msg.getStrContent())) {
                        System.err.println("msgSvrID: " + msg.getMsgSvrID() + " old: " + oldContent + " new: " + msg.getStrContent());
                    }
                }
                msgSvrIDs.put(msg.getMsgSvrID(), msg.getStrContent());
            }
        }

        System.out.println("msgSvrIDs: " + msgSvrIDs.size());
        System.out.println("total: " + total);
    }

    /**
     * 验证一下msg.db是否包含了全部的消息，msgsvrid是否在一个库中是唯一的，通过msgsvrid来判断
     * 结论：是的
     */
    @Test
    public void testMsg2() {
        List<String> files = DBUtils.listDBFile("E:/MemoTrace/app/Database/Msg/pugwoo", "MSG(\\d*)\\.db");
        int total = 0;
        Map<Long, String> msgSvrIDs1 = new HashMap<>();
        Map<Long, String> msgSvrIDs2 = new HashMap<>();
        for (String file : files) {
            DBHelper dbHelper = DBUtils.getDBHelper(file);
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class);
            System.out.println(file + ": " + msgList.size());
            total += msgList.size();
            for (MsgDO msg : msgList) {
                if (file.endsWith("MSG.db")) {
                    msgSvrIDs1.put(msg.getMsgSvrID(), msg.getStrContent());
                } else {
                    msgSvrIDs2.put(msg.getMsgSvrID(), msg.getStrContent());
                }
            }
        }

        // 验证一下msgSvrIDs1和msgSvrIDs2是否一致
        for (Long msgSvrID : msgSvrIDs1.keySet()) {
            if (!msgSvrIDs2.containsKey(msgSvrID) || !Objects.equals(msgSvrIDs1.get(msgSvrID), msgSvrIDs2.get(msgSvrID))) {
                System.err.println("msgSvrID: " + msgSvrID + " msg: " + msgSvrIDs1.get(msgSvrID) + " msg0: " + msgSvrIDs2.get(msgSvrID));
            }
        }
        for (Long msgSvrID : msgSvrIDs2.keySet()) {
            if (!msgSvrIDs1.containsKey(msgSvrID) || !Objects.equals(msgSvrIDs1.get(msgSvrID), msgSvrIDs2.get(msgSvrID))) {
                System.err.println("msgSvrID: " + msgSvrID + " msg: " + msgSvrIDs1.get(msgSvrID) + " msg0: " + msgSvrIDs2.get(msgSvrID));
            }
        }
    }

}
