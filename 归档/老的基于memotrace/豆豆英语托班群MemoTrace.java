package com.pugwoo.weixin_msg;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.entity.SessionAttachInfoDO;
import com.pugwoo.weixin_msg.utils.DBUtils;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

public class 豆豆英语托班群MemoTrace {

    @Test
    public void testSearch() {
        String searchText = "f9ee0e3";

        List<String> files = DBUtils.listDBFile("E:/MemoTrace/app/Database/Msg/pugwoo", ".*\\.db");
        for (String dbPath : files) {
            if (dbPath.contains("\\FTS")) {
                continue;
            }
            System.out.println("dbPath:" + dbPath);
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);

            List<String> tables = dbHelper.getRaw(String.class, "SELECT name FROM sqlite_master WHERE type='table'");
            for (String table : tables) {
                if (table.startsWith("FTS")) {
                    continue;
                }
                List<Map> msgList = dbHelper.getRaw(Map.class, "select * from " + table);
                for (Map map : msgList) {
                    if (JSON.toJson(map).contains(searchText)) {
                        System.out.println("table:" + table + "," + JSON.toJson(map));
                    }
                }
            }
        }
    }

    @Test
    public void test() {
        String dbPath = "E:\\MemoTrace\\app\\Database\\Msg\\pugwoo\\MSG.db";
        DBHelper dbHelper = DBUtils.getDBHelper(dbPath);

        String multiSearchChatMsgDB = "E:\\MemoTrace\\app\\Database\\Msg\\pugwoo\\MultiSearchChatMsg.db";
        DBHelper multiSearchChatMsgDBHelper = DBUtils.getDBHelper(multiSearchChatMsgDB);

        // 豆豆英语托班群
        List<MsgDO> msgList = dbHelper.getAll(MsgDO.class, "where StrTalker='24507125117@chatroom' order by CreateTime desc");

        int totalVideos = 0;
        int notFoundVideos = 0;
        for(MsgDO msg : msgList) {
            System.out.println(from(msg.getCreateTime()) + ": " + msg.getStrContent());

            if (msg.getType().equals(3)) { // 图片

            }

            if (msg.getType().equals(43)) { // 视频
                Long msgSvrID = msg.getMsgSvrID();
                SessionAttachInfoDO one = multiSearchChatMsgDBHelper.getOne(SessionAttachInfoDO.class,
                        "where msgId=?", msgSvrID);
                if (one != null) {
                    System.out.println("视频：" + one.getAttachPath());
                    totalVideos++;
                } else {
                    System.err.println("未找到视频信息,msgSvrId:" + msgSvrID);
                    notFoundVideos++;
                }
            }
        }

        System.out.println("totalVideos:" + totalVideos);
        System.out.println("notFoundVideos:" + notFoundVideos);
    }

    private static LocalDateTime from(Long creatTime) {
        Instant instant = Instant.ofEpochSecond(creatTime);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

}
