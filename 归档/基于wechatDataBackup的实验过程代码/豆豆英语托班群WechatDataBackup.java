package com.pugwoo.weixin_msg;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.protobuf.Msg;
import com.pugwoo.weixin_msg.utils.DBUtils;
import com.pugwoo.wooutils.json.JSON;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

public class 豆豆英语托班群WechatDataBackup {

    @Test
    public void testSearch() {
        String searchText = "f9ee0e3dd4946353e0693fe3539fcc92";

        List<String> files = DBUtils.listDBFile("E:/User/pugwoo/Msg", ".*\\.db");
        files.addAll(DBUtils.listDBFile("E:/User/pugwoo/Msg/Multi", ".*\\.db"));
        for (String dbPath : files) {
            System.out.println("dbPath:" + dbPath);
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);

            List<String> tables = dbHelper.getRaw(String.class, "SELECT name FROM sqlite_master WHERE type='table'");
            for (String table : tables) {
                try {
                    List<Map> msgList = dbHelper.getRaw(Map.class, "select * from " + table);
                    for (Map map : msgList) {
                        if (JSON.toJson(map).contains(searchText)) {
                            System.out.println("【匹配】table:" + table + "," + JSON.toJson(map));
                        }
                    }
                } catch (Exception e) {
                    System.err.println("search " + table + "error: " + e.getMessage());
                }
            }
        }
    }

    // 这个版本应该可以了
    @Test
    public void testNew() throws Exception {
        List<String> dbFile = DBUtils.listDBFile("E:\\weixin-msg\\User\\pugwoo\\Msg\\Multi", "^MSG.*\\.db");

        int totalFoundVideos = 0;
        int notFoundVideos = 0;
        int totalFoundImages = 0;
        int notFoundImages = 0;
        for (String dbPath : dbFile) {
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);

            // 豆豆英语托班群
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class, "where StrTalker='24507125117@chatroom' order by CreateTime desc");
            for(MsgDO msg : msgList) {
                System.out.println(from(msg.getCreateTime()) + ": " + msg.getMsgSvrID() + ": " + msg.getStrContent());

                // 文件 TODO

                if (msg.getType().equals(3)) { // 图片，图片是需要解密的
                    if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
                        Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
                        List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

                        boolean isImageFound = false;
                        for (Msg.SubMessage2 message2 : message2List) {
                            String field2 = message2.getField2();
                            if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("image")) {
                                System.out.println("图片：" + field2);
                                isImageFound = true;
                            }
                            // TODO 如果图片找不到，就找缩略图
                        }

                        if (isImageFound) {
                            totalFoundImages++;
                        } else {
                            System.err.println("未找到图片信息,msgSvrId:" + msg.getMsgSvrID());
                            notFoundImages++;
                        }

                    }
                }

                if (msg.getType().equals(43)) { // 视频
                    if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
                        Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
                        List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

                        boolean isVideoFound = false;
                        for (Msg.SubMessage2 message2 : message2List) {
                            String field2 = message2.getField2();
                            if (field2.toLowerCase().endsWith(".mp4")) {
                                System.out.println("视频：" + field2);
                                isVideoFound = true;
                            }
                            // TODO 如果视频找不到，就找缩略图
                        }

                        if (isVideoFound) {
                            totalFoundVideos++;
                        } else {
                            System.err.println("未找到视频信息,msgSvrId:" + msg.getMsgSvrID());
                            notFoundVideos++;
                        }

                    }
                }
            }
        }

        System.out.println("totalFoundVideos:" + totalFoundVideos);
        System.out.println("notFoundVideos:" + notFoundVideos);
        System.out.println("totalFoundImages:" + totalFoundImages);
        System.out.println("notFoundImages:" + notFoundImages);
    }

    private static LocalDateTime from(Long creatTime) {
        Instant instant = Instant.ofEpochSecond(creatTime);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

}
