package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import net.jpountz.lz4.LZ4Compressor;
import net.jpountz.lz4.LZ4Factory;
import net.jpountz.lz4.LZ4SafeDecompressor;
import org.junit.jupiter.api.Test;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.List;

/**
 * 用于测试MsgUtils的各项功能
 */
public class TestMsgUtils {

    @Test
    public void testParse() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                false, null);
        System.out.println(msgs.size());
    }

    @Test
    public void testParseMedia() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                true, ListUtils.of(4135175694025292375L));
        ListUtils.sortAscNullFirst(msgs, MsgDTO::getMsgTime);
        for (MsgDTO msg : msgs) {
            System.out.println(msg.getMsgTime() + ",msgSvrID:" + msg.getMsgSvrID()
                    + ",type:" + msg.getType() + ",filePath:" + msg.getFilePath()
                    + ", thumb:" + msg.getThumbnailPath());
        }

        // 统计图片中，有filePath的数量和有thumb的数量
        System.out.println("total:" + msgs.size() + ",filePath:" +
                ListUtils.filter(msgs, o -> o.getFilePath() != null).size()
                + ",thumb:" + ListUtils.filter(msgs, o -> o.getThumbnailPath() != null).size()
                + ",none:" + ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).size());

        assert ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).isEmpty();
    }

    @Test
    public void testParseCombineMessage() {
        // 测试解析合并转发消息
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom", false, null);

        // 筛选出合并转发消息
        List<MsgDTO> combineMessages = ListUtils.filter(msgs, msg -> msg.getType() == MsgTypeEnum.COMBINE);

        System.out.println("找到合并转发消息数量: " + combineMessages.size());

        for (MsgDTO msg : combineMessages) {
            System.out.println("合并转发消息 - 时间: " + msg.getMsgTime()
                    + ", msgSvrID: " + msg.getMsgSvrID()
                    + ", 内容: " + msg.getContent());
        }

        // 验证合并转发消息的基本属性
        for (MsgDTO msg : combineMessages) {
            assert msg.getType() == MsgTypeEnum.COMBINE;
            assert msg.getContent() != null;
            assert msg.getContent().contains("合并转发");
        }
    }

    @Test
    public void testLZ4Functionality() {
        // 测试LZ4压缩解压缩功能
        try {
            LZ4Factory factory = LZ4Factory.fastestInstance();
            LZ4Compressor compressor = factory.fastCompressor();
            LZ4SafeDecompressor decompressor = factory.safeDecompressor();

            String testData = "这是一个测试字符串，用于验证LZ4压缩解压缩功能是否正常工作。";
            byte[] originalData = testData.getBytes("UTF-8");

            // 压缩数据
            byte[] compressed = compressor.compress(originalData);

            // 创建带长度前缀的压缩数据（模拟微信格式）
            ByteBuffer buffer = ByteBuffer.allocate(4 + compressed.length);
            buffer.order(ByteOrder.LITTLE_ENDIAN);
            buffer.putInt(originalData.length);
            buffer.put(compressed);
            byte[] compressedWithLength = buffer.array();

            // 解压数据（模拟我们的解压方法）
            ByteBuffer readBuffer = ByteBuffer.wrap(compressedWithLength, 0, 4);
            readBuffer.order(ByteOrder.LITTLE_ENDIAN);
            int originalLength = readBuffer.getInt();

            byte[] decompressed = new byte[originalLength];
            int decompressedLength = decompressor.decompress(compressedWithLength, 4,
                compressed.length, decompressed, 0);

            String result = new String(decompressed, "UTF-8");

            System.out.println("LZ4测试 - 原始数据: " + testData);
            System.out.println("LZ4测试 - 解压结果: " + result);
            System.out.println("LZ4测试 - 压缩前长度: " + originalData.length);
            System.out.println("LZ4测试 - 压缩后长度: " + compressed.length);
            System.out.println("LZ4测试 - 解压后长度: " + decompressedLength);

            assert testData.equals(result);
            assert originalLength == decompressedLength;

            System.out.println("LZ4功能测试通过！");
        } catch (Exception e) {
            System.err.println("LZ4测试失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("LZ4测试失败", e);
        }
    }

}
