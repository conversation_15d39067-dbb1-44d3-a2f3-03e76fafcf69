package com.pugwoo.weixin_msg.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "weixin")
public class GroupConfig {

    /**解密后的消息存放目录，例如 E:\weixin-msg\User*/
    private String decryptMsgDir;

    /**保存的微信图片视频的目录，自定义*/
    private String destSaveDir;

    /**群的配置*/
    private List<GroupDTO> groups;

    @Data
    public static class GroupDTO {
        /**群的中文名称*/
        private String name;
        /**群的唯一标识*/
        private String id;
        /**忽略的消息id*/
        private List<Long> ignoredMsgSvrIds;
    }

}
