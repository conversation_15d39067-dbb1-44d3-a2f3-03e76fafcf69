package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.config.GroupConfig;
import com.pugwoo.weixin_msg.service.ExportMedia;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Date;
import java.util.Scanner;

@SpringBootApplication
public class WeixinMsgApplication {

	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(WeixinMsgApplication.class, args);

		GroupConfig groupConfig = context.getBean(GroupConfig.class);
		ExportMedia.ProcessResult result = ExportMedia.process(groupConfig);

		System.out.println("application ends, result:");
		result.getStats().forEach((k, v) -> System.out.println(k.getName()
				 + (k.isError() ? "【错误】" : "")  + ":" + v));

		// 输入确认再执行
		System.out.println("本次需要执行的任务数量:" + result.getHandles().size() + "，文件数量：" + result.getHandleFileCount());
		System.out.println(DateUtils.format(new Date()) + " 确定执行吗? 输入y执行，输出其他退出");

		Scanner scanner = new Scanner(System.in);
		String input = scanner.nextLine();

		boolean isRun = false;
		if ("y".equalsIgnoreCase(input)) {
			for (Runnable r : result.getHandles()) {
				r.run();
			}
			isRun = true;
		}
		scanner.close();

		System.out.println("程序结束" + DateUtils.format(new Date()) + ":" + (isRun ? "已执行操作" : "未执行任何操作"));
		result.getStats().forEach((k, v) -> System.out.println(k.getName() + ":" + v));
	}

}
