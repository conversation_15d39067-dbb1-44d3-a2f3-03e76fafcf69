package com.pugwoo.weixin_msg.utils;

import lombok.SneakyThrows;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileTime;
import java.util.ArrayList;
import java.util.List;

public class FileUtils {

    public static void copy(String sourcePath, String destPath) throws IOException{
        Path source = Paths.get(sourcePath);
        Path destination = Paths.get(destPath);

        Files.createDirectories(destination.getParent());
        Files.copy(source, destination, StandardCopyOption.COPY_ATTRIBUTES, StandardCopyOption.REPLACE_EXISTING);
        FileTime lastModifiedTime = Files.getLastModifiedTime(source);
        Files.setLastModifiedTime(destination, lastModifiedTime);
    }

    public static void delete(String path) throws IOException {
        Files.deleteIfExists(Paths.get(path));
    }

    public static void move(String path1, String path2) throws IOException {
        Files.move(Paths.get(path1), Paths.get(path2), StandardCopyOption.REPLACE_EXISTING);
    }

    @SneakyThrows
    public static List<String> listFiles(String dir) {
        List<String> filePaths = new ArrayList<>();
        if (!FileUtils.isExist(dir)) {
            return filePaths;
        }
        Files.walk(Paths.get(dir))
                .filter(Files::isRegularFile)
                .forEach(path -> filePaths.add(path.toAbsolutePath().toString()));
        return filePaths;
    }

    public static boolean isExist(String absolutePath) {
        return Files.exists(Paths.get(absolutePath));
    }

    /**
     * 2025年1月21日23:25:24 由于微信的文件大小都会发生变化，故只判断文件相差小于256字节，就认为是相同
     */
    public static boolean isSameFileQuickCheck(String sourcePath, String destPath) {
        File sourceFile = new File(sourcePath);
        File destFile = new File(destPath);

        // Check if files exist
        if (!sourceFile.exists() || !destFile.exists()) {
            return false;
        }

        // Compare file sizes
        long sourceFileLength = sourceFile.length();
        long destFileLength = destFile.length();

        return Math.abs(sourceFileLength - destFileLength) < 256;

//        if (sourceFileLength != destFileLength) {
//            return false;
//        }

//        try (FileInputStream sourceStream = new FileInputStream(sourceFile);
//             FileInputStream destStream = new FileInputStream(destFile)) {
//
//            byte[] sourceBuffer = new byte[(int) Math.min(sourceFileLength, 512 * 1024)];
//            byte[] destBuffer = new byte[(int) Math.min(sourceFileLength, 512 * 1024)];
//
//            if (sourceStream.read(sourceBuffer) != destStream.read(destBuffer)) {
//                return false;
//            }
//
//            if (!Arrays.equals(sourceBuffer, destBuffer)) {
//                return false;
//            } else {
//                return true;
//            }
//        } catch (IOException e) {
//            throw new RuntimeException("Error while comparing files", e);
//        }
    }
}
