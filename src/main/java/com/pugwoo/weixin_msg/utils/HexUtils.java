package com.pugwoo.weixin_msg.utils;

public class HexUtils {

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xFF & b);
            if (hex.length() == 1) {
                hexString.append('0'); // 保证每个字节转为两位十六进制数
            }
            hexString.append(hex).append("");
        }
        return hexString.toString();
    }

}
