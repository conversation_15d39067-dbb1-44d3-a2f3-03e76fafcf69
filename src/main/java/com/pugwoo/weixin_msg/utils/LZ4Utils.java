package com.pugwoo.weixin_msg.utils;

import net.jpountz.lz4.LZ4Exception;
import net.jpountz.lz4.LZ4Factory;
import net.jpountz.lz4.LZ4SafeDecompressor;

import java.util.Arrays;

public class LZ4Utils {

    /**
     * 支持自适应解压
     */
    public static byte[] decompress(byte[] src) {
        if (src == null) {
            return null;
        }
        LZ4Factory factory = LZ4Factory.fastestInstance();
        LZ4SafeDecompressor decompressor = factory.safeDecompressor();

        // 初始缓冲：src.length * 256（确保至少为 1KB）
        int destLen = Math.max(1024, src.length * 256);
        // 为防止无限制增长，限制一个合理的最大值（这里取 1024MB，视场景调整）
        final int MAX_BUFFER = Math.min(Integer.MAX_VALUE, 1024 * 1024 * 1024);

        if (destLen > MAX_BUFFER) {
            destLen = MAX_BUFFER;
        }

        byte[] dest = new byte[destLen];

        while (true) {
            try {
                // 调用 safe 解压，返回解压后的实际长度
                int decompressed = decompressor.decompress(src, 0, src.length, dest, 0);

                // 如果返回的长度刚好等于给定数组长度，说明可能给的数组太小（或正好命中边界），
                // 按注释要求把数组放大 *512 再试
                if (decompressed == dest.length) {
                    long newLen = (long) dest.length * 512L;
                    if (newLen > MAX_BUFFER) newLen = MAX_BUFFER;
                    if ((int) newLen == dest.length) { // 无法继续增大
                        throw new IllegalArgumentException("Decompressed size likely >= max allowed buffer (" + MAX_BUFFER + ")");
                    }
                    dest = Arrays.copyOf(dest, (int) newLen);
                    continue;
                }

                // 返回真实长度的数组切片
                byte[] result = Arrays.copyOf(dest, decompressed);

                // 去掉尾部\0的byte
                int lastNonZeroIndex = result.length - 1;
                while (lastNonZeroIndex >= 0 && result[lastNonZeroIndex] == 0) {
                    lastNonZeroIndex--;
                }
                result = Arrays.copyOf(result, lastNonZeroIndex + 1);

                return result;
            } catch (LZ4Exception e) {
                // decompressor 报错，通常是目标数组太小；扩大缓冲并重试
                long newLen = (long) dest.length * 2L; // 先乘 2 再重试（避免每次都放大 512）
                if (newLen > MAX_BUFFER) newLen = MAX_BUFFER;
                if ((int) newLen == dest.length) {
                    throw new IllegalArgumentException("Unable to decompress: required output exceeds max allowed buffer (" + MAX_BUFFER + ")", e);
                }
                dest = Arrays.copyOf(dest, (int) newLen);
            }
        }
    }


}
