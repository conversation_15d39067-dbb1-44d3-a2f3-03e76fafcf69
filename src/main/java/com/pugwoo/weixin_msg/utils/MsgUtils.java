package com.pugwoo.weixin_msg.utils;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.protobuf.Msg;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MsgUtils {

    /**
     * 解析由wechatDataBackup生成的文件中的消息
     *
     * @param decryptMsgDir wechatDataBackup生成的User文件夹的位置【必须】
     * @param groupId 群的id，TODO 待确定能不能通过群id拿到群名称
     * @param isOnlyMedia 是否只处理图片、视频、文件
     * @param ignoredMsgIds 指定的忽略不处理的消息id
     * @return 处理后的消息
     */
    @SneakyThrows
    public static List<MsgDTO> parseMsg(String decryptMsgDir, String groupId, boolean isOnlyMedia, List<Long> ignoredMsgIds) {
        List<MsgDTO> result = new ArrayList<>();

        List<String> dbFiles = DBUtils.listDBFile(decryptMsgDir + "\\pugwoo\\Msg\\Multi", "^MSG.*\\.db");
        for (String dbPath : dbFiles) {
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class, "where StrTalker=?", groupId);

            for (MsgDO msg : msgList) {
                if (ignoredMsgIds != null && ignoredMsgIds.contains(msg.getMsgSvrID())) {
                    continue;
                }

                MsgTypeEnum msgTypeEnum = transToMyType(msg.getType(), msg.getSubType());

                if (isOnlyMedia && !(msgTypeEnum == MsgTypeEnum.IMAGE || msgTypeEnum == MsgTypeEnum.VIDEO
                        || msgTypeEnum == MsgTypeEnum.FILE)) {
                    continue; // 只处理图片、视频、文件
                }

                MsgDTO msgDTO = new MsgDTO();
                result.add(msgDTO);
                msgDTO.setMsgTime(from(msg.getCreateTime()));
                msgDTO.setMsgSvrID(msg.getMsgSvrID());
                msgDTO.setContent(msg.getStrContent());
                msgDTO.setType(msgTypeEnum);

                // 最重要的，解析文件路径
                if (msgTypeEnum == MsgTypeEnum.IMAGE) {
                    parseImage(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.VIDEO) {
                    parseVideo(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.COMBINE) {
                    parseCombineMessage(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.FILE) {
                    throw new RuntimeException("TODO 工具尚不支持文件的解析，msgSvrID=" + msg.getMsgSvrID() + "，dbPath:" + dbPath
                            + "，time:" + from(msg.getCreateTime()));
                }
            }
        }

        return result;
    }

    @SneakyThrows
    private static void parseVideo(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isVideoFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".mp4")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isVideoFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }

            if (!isVideoFound) { // 如果视频找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".jpg")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("视频消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    @SneakyThrows
    private static void parseImage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isImageFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("image")
                    && !field2.toLowerCase().contains("thumb")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isImageFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }
            if (!isImageFound) { // 如果图片找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("thumb")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("图片消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    private static String getFileUuid(String file) {
        return file.substring(file.lastIndexOf("\\") + 1, file.lastIndexOf("."));
    }

    private static MsgTypeEnum transToMyType(Integer type, Integer subType) {
        if (type == null) {
            return MsgTypeEnum.UNKNOWN;
        }
        if (type.equals(0)) {
            return MsgTypeEnum.TEXT;
        }
        if (type.equals(3)) {
            return MsgTypeEnum.IMAGE;
        }
        if (type.equals(43)) {
            return MsgTypeEnum.VIDEO;
        }
        if (type.equals(49) && subType != null && subType.equals(6)) {
            return MsgTypeEnum.FILE;
        }
        if (type.equals(49) && subType != null && subType.equals(19)) {
            return MsgTypeEnum.COMBINE;
        }
        return MsgTypeEnum.OTHERS;
    }

    private static LocalDateTime from(Long creatTime) {
        Instant instant = Instant.ofEpochSecond(creatTime);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    /**
     * 解析合并转发消息
     * @param msgDTO 消息DTO
     * @param msg 原始消息
     * @param decryptMsgDir 解密消息目录
     */
    @SneakyThrows
    private static void parseCombineMessage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        msgDTO.setType(MsgTypeEnum.COMBINE);

        // 解析XML内容获取基本信息
        String xmlContent = msg.getStrContent();
        parseCombineXmlContent(msgDTO, xmlContent);

        // 解析CompressContent获取详细聊天记录
        if (msg.getCompressContent() != null && msg.getCompressContent().length > 0) {
            parseCombineCompressContent(msgDTO, msg.getCompressContent());
        }
    }

    /**
     * 解析合并转发消息的XML内容
     * @param msgDTO 消息DTO
     * @param xmlContent XML内容
     */
    private static void parseCombineXmlContent(MsgDTO msgDTO, String xmlContent) {
        try {
            String title = extractXmlValue(xmlContent, "title");
            String des = extractXmlValue(xmlContent, "des");

            String content;
            if (title != null && !title.trim().isEmpty()) {
                content = "合并转发: " + title;
            } else if (des != null && !des.trim().isEmpty()) {
                content = "合并转发: " + des;
            } else {
                content = "合并转发消息";
            }

            msgDTO.setContent(content);
        } catch (Exception e) {
            msgDTO.setContent("合并转发消息");
        }
    }

    /**
     * 解析合并转发消息的CompressContent
     * @param msgDTO 消息DTO
     * @param compressContent 压缩内容
     */
    private static void parseCombineCompressContent(MsgDTO msgDTO, byte[] compressContent) {
        try {
            // 使用LZ4解压缩
            String decompressedXml = decompressLZ4(compressContent);
            if (decompressedXml != null && !decompressedXml.trim().isEmpty()) {
                // 解析聊天记录
                String chatRecords = parseChatRecordsFromXml(decompressedXml);
                if (chatRecords != null && !chatRecords.trim().isEmpty()) {
                    String currentContent = msgDTO.getContent();
                    msgDTO.setContent(currentContent + "\n聊天记录摘要:\n" + chatRecords);
                }
            }
        } catch (Exception e) {
            // 解析失败时不影响基本功能，保持原有内容
        }
    }

    /**
     * 使用LZ4解压缩数据
     * @param compressedData 压缩数据
     * @return 解压后的字符串
     */
    private static String decompressLZ4(byte[] compressedData) {
        try {
            if (compressedData == null) {
                return null;
            }
            byte[] decompressed = LZ4Utils.decompress(compressedData);
            return new String(decompressed, "UTF-8");
        } catch (Exception e) {
            log.error("decompressLZ4 fail", e);
            return null;
        }
    }

    /**
     * 从XML中解析聊天记录
     * @param xmlContent XML内容
     * @return 格式化的聊天记录
     */
    private static String parseChatRecordsFromXml(String xmlContent) {
        try {
            List<String> records = new ArrayList<>();

            // 使用正则表达式提取recorditem节点
            Pattern recordPattern = Pattern.compile("<recorditem[^>]*>(.*?)</recorditem>", Pattern.DOTALL);
            Matcher recordMatcher = recordPattern.matcher(xmlContent);

            int count = 0;
            int maxRecords = 5; // 最多显示5条记录

            while (recordMatcher.find() && count < maxRecords) {
                String recordContent = recordMatcher.group(1);

                String nickname = extractXmlValue(recordContent, "nickname");
                String content = extractXmlValue(recordContent, "content");
                String timeStr = extractXmlValue(recordContent, "time");

                if (nickname != null && content != null) {
                    String timeDisplay = "";
                    if (timeStr != null && timeStr.matches("\\d+")) {
                        try {
                            long timestamp = Long.parseLong(timeStr);
                            LocalDateTime time = from(timestamp);
                            timeDisplay = "[" + time + "] ";
                        } catch (Exception e) {
                            timeDisplay = "[" + timeStr + "] ";
                        }
                    }

                    records.add(timeDisplay + nickname + ": " + content);
                    count++;
                }
            }

            // 计算总记录数
            Matcher totalMatcher = recordPattern.matcher(xmlContent);
            int totalCount = 0;
            while (totalMatcher.find()) {
                totalCount++;
            }

            if (!records.isEmpty()) {
                StringBuilder result = new StringBuilder();
                for (String record : records) {
                    result.append(record).append("\n");
                }

                if (totalCount > maxRecords) {
                    result.append("... 还有 ").append(totalCount - maxRecords).append(" 条消息");
                }

                return result.toString().trim();
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从XML中提取指定标签的值
     * @param xml XML内容
     * @param tagName 标签名
     * @return 标签值
     */
    private static String extractXmlValue(String xml, String tagName) {
        if (xml == null || tagName == null) {
            return null;
        }

        try {
            // 处理CDATA格式
            Pattern cdataPattern = Pattern.compile("<" + tagName + "><!\\[CDATA\\[(.*?)\\]\\]></" + tagName + ">", Pattern.DOTALL);
            Matcher cdataMatcher = cdataPattern.matcher(xml);
            if (cdataMatcher.find()) {
                return cdataMatcher.group(1);
            }

            // 处理普通格式
            Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(xml);
            if (matcher.find()) {
                return matcher.group(1);
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
