package com.pugwoo.weixin_msg.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("MSG")
public class MsgDO {

    @Column(value = "localId", isKey = true, isAutoIncrement = true)
    private Long localId;

    /**
     * 发送者的唯一id，这个id应该是个本地id，例如35，它不是服务器上全局的
     */
    @Column(value = "TalkerId")
    private Integer talkerId;

    /**
     * 发送者，例如：24507125117@chatroom
     */
    @Column(value = "StrTalker")
    private String strTalker;

    /**
     * 结论：
     *1. msgSvrID相同，其strContent肯定相同，msgSvrID有十万分之一是重复的，检查了下数据，重复的msgSvgID除了0外，其它的strContent都是相同的。msgSvgID=0的消息一般是没用的，也可以用msgSvrID+Sequence来唯一确定一条消息。
     */
    @Column(value = "MsgSvrID")
    private Long msgSvrID;

    @Column(value = "Type")
    private Integer type;

    @Column(value = "SubType")
    private Integer subType;

    @Column(value = "IsSender")
    private Integer isSender;

    @Column(value = "CreateTime")
    private Long createTime;

    @Column(value = "Sequence")
    private Integer sequence;

    @Column(value = "StatusEx")
    private Integer statusEx;

    @Column(value = "FlagEx")
    private Integer flagEx;

    @Column(value = "Status")
    private Integer status;

    @Column(value = "MsgServerSeq")
    private Integer msgServerSeq;

    @Column(value = "MsgSequence")
    private Integer msgSequence;

    @Column(value = "StrContent")
    private String strContent;

    @Column(value = "DisplayContent")
    private String displayContent;

    @Column(value = "Reserved0")
    private Integer reserved0;

    @Column(value = "Reserved1")
    private Integer reserved1;

    @Column(value = "Reserved2")
    private Integer reserved2;

    @Column(value = "Reserved3")
    private Integer reserved3;

    @Column(value = "Reserved4")
    private String reserved4;

    @Column(value = "Reserved5")
    private String reserved5;

    @Column(value = "Reserved6")
    private String reserved6;

    @Column(value = "CompressContent")
    private byte[] compressContent;

    @Column(value = "BytesExtra")
    private byte[] bytesExtra;

    @Column(value = "BytesTrans")
    private byte[] bytesTrans;

}