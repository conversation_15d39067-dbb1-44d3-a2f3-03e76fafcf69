package com.pugwoo.weixin_msg.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum MsgTypeEnum {

    TEXT("TEXT", "文本"),

    IMAGE("IMAGE", "图片"),

    VIDEO("VIDEO", "视频"),

    FILE("FILE", "文件"),

    COMBINE("COMBINE", "合并转发聊天"),

    OTHERS("OTHERS", "其他"),

    UNKNOWN("UNKNOWN", "未知");

    final private String code;
    final private String name;

    MsgTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MsgTypeEnum getByCode(String code) {
        for (MsgTypeEnum e : MsgTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getNameByCode(String code) {
        MsgTypeEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}