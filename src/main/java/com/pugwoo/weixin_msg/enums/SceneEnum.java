package com.pugwoo.weixin_msg.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 用于统计的场景分类
 */
@Getter
public enum SceneEnum {

    ERROR("ERROR", "处理异常", true),

    SOURCE_ORIGIN_EXIST_NO("SOURCE_ORIGIN_EXIST_NO", "原文件|目标文件不存在", false),

    SOURCE_ORIGIN_EXIST_ORIGIN("SOURCE_ORIGIN_EXIST_ORIGIN", "原文件|目标原文件", false),

    SOURCE_ORIGIN_EXIST_ORIGIN_DUP_WARN("SOURCE_ORIGIN_EXIST_ORIGIN_DUP_WARN", "原文件|目标原文件(但文件不一样)", true),

    SOURCE_ORIGIN_EXIST_THUMBNAIL("SOURCE_ORIGIN_EXIST_THUMBNAIL", "原文件|目标缩略图", false),

    SOURCE_THUMBNAIL_EXIST_NO("SOURCE_THUMBNAIL_EXIST_NO", "缩略文件|目标文件不存在", false),

    SOURCE_THUMBNAIL_EXIST_ORIGIN("SOURCE_THUMBNAIL_EXIST_ORIGIN",
            "缩略文件|目标原文件", false),

    SOURCE_THUMBNAIL_EXIST_THUMBNAIL("SOURCE_THUMBNAIL_EXIST_THUMBNAIL",
            "缩略文件|目标缩略文件", false),

    SOURCE_THUMBNAIL_EXIST_THUMBNAIL_DUP_WARN("SOURCE_THUMBNAIL_EXIST_THUMBNAIL_DUP_WARN",
            "缩略文件|目标缩略文件(但文件不一样)", false),

    SOURCE_NO_EXIST_ORIGIN("SOURCE_NO_EXIST_ORIGIN", "原文件不存在|目标文件是原文件", false),

    SOURCE_NO_EXIST_THUMBNAIL("SOURCE_NO_EXIST_THUMBNAIL", "原文件不存在|目标缩略图", false),

    SOURCE_ORIGIN_THUMBNAIL_NOT_EXIST("SOURCE_ORIGIN_THUMBNAIL_NOT_EXIST", "原文件和缩略图不存在", true)
    ;

    final private String code;
    final private String name;

    /**是否是异常场景*/
    final private boolean isError;

    SceneEnum(String code, String name, boolean isError) {
        this.code = code;
        this.name = name;
        this.isError = isError;
    }

    public static SceneEnum getByCode(String code) {
        for (SceneEnum e : SceneEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        SceneEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}