package com.pugwoo.weixin_msg.dto;

import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 解析出来的处理后的有用消息
 */
@Data
public class MsgDTO {

    /**消息时间*/
    private LocalDateTime msgTime;

    /**唯一标识消息的id*/
    private Long msgSvrID;

    /**消息内容*/
    private String content;

    /**文件名本身的uuid*/
    private String fileUuid;

    /**消息类型，我自己定义的*/
    private MsgTypeEnum type;

    /**图片视频文件的路径(绝对路径)，这里一定是源文件，而且这个路径可以在解密后的文件中找到。如果这个值是空的，那就是找不到了*/
    private String filePath;

    /**缩略图路径(绝对路径)*/
    private String thumbnailPath;

    // 说明：正常情况下，如果MsgTypeEnum是IMAGE/VIDEO/FILE，那么filePath和thumbnailPath至少一个有值。

}
