// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: msg.proto
// Protobuf Java Version: 4.29.1

package com.pugwoo.weixin_msg.protobuf;

public final class Msg {
  private Msg() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 1,
      /* suffix= */ "",
      Msg.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SubMessage1OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.pugwoo.weixin_msg.protobuf.SubMessage1)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 field1 = 1;</code>
     * @return The field1.
     */
    int getField1();

    /**
     * <code>int32 field2 = 2;</code>
     * @return The field2.
     */
    int getField2();
  }
  /**
   * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.SubMessage1}
   */
  public static final class SubMessage1 extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.pugwoo.weixin_msg.protobuf.SubMessage1)
      SubMessage1OrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 1,
        /* suffix= */ "",
        SubMessage1.class.getName());
    }
    // Use SubMessage1.newBuilder() to construct.
    private SubMessage1(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SubMessage1() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.class, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder.class);
    }

    public static final int FIELD1_FIELD_NUMBER = 1;
    private int field1_ = 0;
    /**
     * <code>int32 field1 = 1;</code>
     * @return The field1.
     */
    @java.lang.Override
    public int getField1() {
      return field1_;
    }

    public static final int FIELD2_FIELD_NUMBER = 2;
    private int field2_ = 0;
    /**
     * <code>int32 field2 = 2;</code>
     * @return The field2.
     */
    @java.lang.Override
    public int getField2() {
      return field2_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (field1_ != 0) {
        output.writeInt32(1, field1_);
      }
      if (field2_ != 0) {
        output.writeInt32(2, field2_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (field1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, field1_);
      }
      if (field2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, field2_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1)) {
        return super.equals(obj);
      }
      com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 other = (com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1) obj;

      if (getField1()
          != other.getField1()) return false;
      if (getField2()
          != other.getField2()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FIELD1_FIELD_NUMBER;
      hash = (53 * hash) + getField1();
      hash = (37 * hash) + FIELD2_FIELD_NUMBER;
      hash = (53 * hash) + getField2();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.SubMessage1}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.pugwoo.weixin_msg.protobuf.SubMessage1)
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.class, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder.class);
      }

      // Construct using com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        field1_ = 0;
        field2_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getDefaultInstanceForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance();
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 build() {
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 buildPartial() {
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 result = new com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.field1_ = field1_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.field2_ = field2_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1) {
          return mergeFrom((com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 other) {
        if (other == com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance()) return this;
        if (other.getField1() != 0) {
          setField1(other.getField1());
        }
        if (other.getField2() != 0) {
          setField2(other.getField2());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                field1_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                field2_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int field1_ ;
      /**
       * <code>int32 field1 = 1;</code>
       * @return The field1.
       */
      @java.lang.Override
      public int getField1() {
        return field1_;
      }
      /**
       * <code>int32 field1 = 1;</code>
       * @param value The field1 to set.
       * @return This builder for chaining.
       */
      public Builder setField1(int value) {

        field1_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 field1 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearField1() {
        bitField0_ = (bitField0_ & ~0x00000001);
        field1_ = 0;
        onChanged();
        return this;
      }

      private int field2_ ;
      /**
       * <code>int32 field2 = 2;</code>
       * @return The field2.
       */
      @java.lang.Override
      public int getField2() {
        return field2_;
      }
      /**
       * <code>int32 field2 = 2;</code>
       * @param value The field2 to set.
       * @return This builder for chaining.
       */
      public Builder setField2(int value) {

        field2_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 field2 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearField2() {
        bitField0_ = (bitField0_ & ~0x00000002);
        field2_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.pugwoo.weixin_msg.protobuf.SubMessage1)
    }

    // @@protoc_insertion_point(class_scope:com.pugwoo.weixin_msg.protobuf.SubMessage1)
    private static final com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1();
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubMessage1>
        PARSER = new com.google.protobuf.AbstractParser<SubMessage1>() {
      @java.lang.Override
      public SubMessage1 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SubMessage1> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubMessage1> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SubMessage2OrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.pugwoo.weixin_msg.protobuf.SubMessage2)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 field1 = 1;</code>
     * @return The field1.
     */
    int getField1();

    /**
     * <code>string field2 = 2;</code>
     * @return The field2.
     */
    java.lang.String getField2();
    /**
     * <code>string field2 = 2;</code>
     * @return The bytes for field2.
     */
    com.google.protobuf.ByteString
        getField2Bytes();
  }
  /**
   * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.SubMessage2}
   */
  public static final class SubMessage2 extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.pugwoo.weixin_msg.protobuf.SubMessage2)
      SubMessage2OrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 1,
        /* suffix= */ "",
        SubMessage2.class.getName());
    }
    // Use SubMessage2.newBuilder() to construct.
    private SubMessage2(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SubMessage2() {
      field2_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.class, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder.class);
    }

    public static final int FIELD1_FIELD_NUMBER = 1;
    private int field1_ = 0;
    /**
     * <code>int32 field1 = 1;</code>
     * @return The field1.
     */
    @java.lang.Override
    public int getField1() {
      return field1_;
    }

    public static final int FIELD2_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object field2_ = "";
    /**
     * <code>string field2 = 2;</code>
     * @return The field2.
     */
    @java.lang.Override
    public java.lang.String getField2() {
      java.lang.Object ref = field2_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        field2_ = s;
        return s;
      }
    }
    /**
     * <code>string field2 = 2;</code>
     * @return The bytes for field2.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getField2Bytes() {
      java.lang.Object ref = field2_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        field2_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (field1_ != 0) {
        output.writeInt32(1, field1_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(field2_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, field2_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (field1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, field1_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(field2_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, field2_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2)) {
        return super.equals(obj);
      }
      com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 other = (com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2) obj;

      if (getField1()
          != other.getField1()) return false;
      if (!getField2()
          .equals(other.getField2())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FIELD1_FIELD_NUMBER;
      hash = (53 * hash) + getField1();
      hash = (37 * hash) + FIELD2_FIELD_NUMBER;
      hash = (53 * hash) + getField2().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.SubMessage2}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.pugwoo.weixin_msg.protobuf.SubMessage2)
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.class, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder.class);
      }

      // Construct using com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        field1_ = 0;
        field2_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getDefaultInstanceForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.getDefaultInstance();
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 build() {
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 buildPartial() {
        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 result = new com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.field1_ = field1_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.field2_ = field2_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2) {
          return mergeFrom((com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 other) {
        if (other == com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.getDefaultInstance()) return this;
        if (other.getField1() != 0) {
          setField1(other.getField1());
        }
        if (!other.getField2().isEmpty()) {
          field2_ = other.field2_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                field1_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                field2_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int field1_ ;
      /**
       * <code>int32 field1 = 1;</code>
       * @return The field1.
       */
      @java.lang.Override
      public int getField1() {
        return field1_;
      }
      /**
       * <code>int32 field1 = 1;</code>
       * @param value The field1 to set.
       * @return This builder for chaining.
       */
      public Builder setField1(int value) {

        field1_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 field1 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearField1() {
        bitField0_ = (bitField0_ & ~0x00000001);
        field1_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object field2_ = "";
      /**
       * <code>string field2 = 2;</code>
       * @return The field2.
       */
      public java.lang.String getField2() {
        java.lang.Object ref = field2_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          field2_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string field2 = 2;</code>
       * @return The bytes for field2.
       */
      public com.google.protobuf.ByteString
          getField2Bytes() {
        java.lang.Object ref = field2_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          field2_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string field2 = 2;</code>
       * @param value The field2 to set.
       * @return This builder for chaining.
       */
      public Builder setField2(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        field2_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string field2 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearField2() {
        field2_ = getDefaultInstance().getField2();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string field2 = 2;</code>
       * @param value The bytes for field2 to set.
       * @return This builder for chaining.
       */
      public Builder setField2Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        field2_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:com.pugwoo.weixin_msg.protobuf.SubMessage2)
    }

    // @@protoc_insertion_point(class_scope:com.pugwoo.weixin_msg.protobuf.SubMessage2)
    private static final com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2();
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SubMessage2>
        PARSER = new com.google.protobuf.AbstractParser<SubMessage2>() {
      @java.lang.Override
      public SubMessage2 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SubMessage2> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SubMessage2> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MessageBytesExtraOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.pugwoo.weixin_msg.protobuf.MessageBytesExtra)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     * @return Whether the message1 field is set.
     */
    boolean hasMessage1();
    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     * @return The message1.
     */
    com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getMessage1();
    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     */
    com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder getMessage1OrBuilder();

    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> 
        getMessage2List();
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getMessage2(int index);
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    int getMessage2Count();
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    java.util.List<? extends com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder> 
        getMessage2OrBuilderList();
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder getMessage2OrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.MessageBytesExtra}
   */
  public static final class MessageBytesExtra extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:com.pugwoo.weixin_msg.protobuf.MessageBytesExtra)
      MessageBytesExtraOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 1,
        /* suffix= */ "",
        MessageBytesExtra.class.getName());
    }
    // Use MessageBytesExtra.newBuilder() to construct.
    private MessageBytesExtra(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MessageBytesExtra() {
      message2_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.class, com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.Builder.class);
    }

    private int bitField0_;
    public static final int MESSAGE1_FIELD_NUMBER = 1;
    private com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 message1_;
    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     * @return Whether the message1 field is set.
     */
    @java.lang.Override
    public boolean hasMessage1() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     * @return The message1.
     */
    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getMessage1() {
      return message1_ == null ? com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance() : message1_;
    }
    /**
     * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
     */
    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder getMessage1OrBuilder() {
      return message1_ == null ? com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance() : message1_;
    }

    public static final int MESSAGE2_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> message2_;
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> getMessage2List() {
      return message2_;
    }
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder> 
        getMessage2OrBuilderList() {
      return message2_;
    }
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    @java.lang.Override
    public int getMessage2Count() {
      return message2_.size();
    }
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getMessage2(int index) {
      return message2_.get(index);
    }
    /**
     * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
     */
    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder getMessage2OrBuilder(
        int index) {
      return message2_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMessage1());
      }
      for (int i = 0; i < message2_.size(); i++) {
        output.writeMessage(3, message2_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMessage1());
      }
      for (int i = 0; i < message2_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, message2_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra)) {
        return super.equals(obj);
      }
      com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra other = (com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra) obj;

      if (hasMessage1() != other.hasMessage1()) return false;
      if (hasMessage1()) {
        if (!getMessage1()
            .equals(other.getMessage1())) return false;
      }
      if (!getMessage2List()
          .equals(other.getMessage2List())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMessage1()) {
        hash = (37 * hash) + MESSAGE1_FIELD_NUMBER;
        hash = (53 * hash) + getMessage1().hashCode();
      }
      if (getMessage2Count() > 0) {
        hash = (37 * hash) + MESSAGE2_FIELD_NUMBER;
        hash = (53 * hash) + getMessage2List().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.pugwoo.weixin_msg.protobuf.MessageBytesExtra}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.pugwoo.weixin_msg.protobuf.MessageBytesExtra)
        com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtraOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.class, com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.Builder.class);
      }

      // Construct using com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getMessage1FieldBuilder();
          getMessage2FieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        message1_ = null;
        if (message1Builder_ != null) {
          message1Builder_.dispose();
          message1Builder_ = null;
        }
        if (message2Builder_ == null) {
          message2_ = java.util.Collections.emptyList();
        } else {
          message2_ = null;
          message2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra getDefaultInstanceForType() {
        return com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.getDefaultInstance();
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra build() {
        com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra buildPartial() {
        com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra result = new com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra result) {
        if (message2Builder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            message2_ = java.util.Collections.unmodifiableList(message2_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.message2_ = message2_;
        } else {
          result.message2_ = message2Builder_.build();
        }
      }

      private void buildPartial0(com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.message1_ = message1Builder_ == null
              ? message1_
              : message1Builder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra) {
          return mergeFrom((com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra other) {
        if (other == com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra.getDefaultInstance()) return this;
        if (other.hasMessage1()) {
          mergeMessage1(other.getMessage1());
        }
        if (message2Builder_ == null) {
          if (!other.message2_.isEmpty()) {
            if (message2_.isEmpty()) {
              message2_ = other.message2_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureMessage2IsMutable();
              message2_.addAll(other.message2_);
            }
            onChanged();
          }
        } else {
          if (!other.message2_.isEmpty()) {
            if (message2Builder_.isEmpty()) {
              message2Builder_.dispose();
              message2Builder_ = null;
              message2_ = other.message2_;
              bitField0_ = (bitField0_ & ~0x00000002);
              message2Builder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getMessage2FieldBuilder() : null;
            } else {
              message2Builder_.addAllMessages(other.message2_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getMessage1FieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 26: {
                com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 m =
                    input.readMessage(
                        com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.parser(),
                        extensionRegistry);
                if (message2Builder_ == null) {
                  ensureMessage2IsMutable();
                  message2_.add(m);
                } else {
                  message2Builder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 message1_;
      private com.google.protobuf.SingleFieldBuilder<
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder> message1Builder_;
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       * @return Whether the message1 field is set.
       */
      public boolean hasMessage1() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       * @return The message1.
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 getMessage1() {
        if (message1Builder_ == null) {
          return message1_ == null ? com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance() : message1_;
        } else {
          return message1Builder_.getMessage();
        }
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public Builder setMessage1(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 value) {
        if (message1Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          message1_ = value;
        } else {
          message1Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public Builder setMessage1(
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder builderForValue) {
        if (message1Builder_ == null) {
          message1_ = builderForValue.build();
        } else {
          message1Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public Builder mergeMessage1(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1 value) {
        if (message1Builder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            message1_ != null &&
            message1_ != com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance()) {
            getMessage1Builder().mergeFrom(value);
          } else {
            message1_ = value;
          }
        } else {
          message1Builder_.mergeFrom(value);
        }
        if (message1_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public Builder clearMessage1() {
        bitField0_ = (bitField0_ & ~0x00000001);
        message1_ = null;
        if (message1Builder_ != null) {
          message1Builder_.dispose();
          message1Builder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder getMessage1Builder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMessage1FieldBuilder().getBuilder();
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder getMessage1OrBuilder() {
        if (message1Builder_ != null) {
          return message1Builder_.getMessageOrBuilder();
        } else {
          return message1_ == null ?
              com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.getDefaultInstance() : message1_;
        }
      }
      /**
       * <code>.com.pugwoo.weixin_msg.protobuf.SubMessage1 message1 = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder> 
          getMessage1FieldBuilder() {
        if (message1Builder_ == null) {
          message1Builder_ = new com.google.protobuf.SingleFieldBuilder<
              com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage1OrBuilder>(
                  getMessage1(),
                  getParentForChildren(),
                  isClean());
          message1_ = null;
        }
        return message1Builder_;
      }

      private java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> message2_ =
        java.util.Collections.emptyList();
      private void ensureMessage2IsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          message2_ = new java.util.ArrayList<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2>(message2_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder> message2Builder_;

      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> getMessage2List() {
        if (message2Builder_ == null) {
          return java.util.Collections.unmodifiableList(message2_);
        } else {
          return message2Builder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public int getMessage2Count() {
        if (message2Builder_ == null) {
          return message2_.size();
        } else {
          return message2Builder_.getCount();
        }
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 getMessage2(int index) {
        if (message2Builder_ == null) {
          return message2_.get(index);
        } else {
          return message2Builder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder setMessage2(
          int index, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 value) {
        if (message2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessage2IsMutable();
          message2_.set(index, value);
          onChanged();
        } else {
          message2Builder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder setMessage2(
          int index, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder builderForValue) {
        if (message2Builder_ == null) {
          ensureMessage2IsMutable();
          message2_.set(index, builderForValue.build());
          onChanged();
        } else {
          message2Builder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder addMessage2(com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 value) {
        if (message2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessage2IsMutable();
          message2_.add(value);
          onChanged();
        } else {
          message2Builder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder addMessage2(
          int index, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2 value) {
        if (message2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMessage2IsMutable();
          message2_.add(index, value);
          onChanged();
        } else {
          message2Builder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder addMessage2(
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder builderForValue) {
        if (message2Builder_ == null) {
          ensureMessage2IsMutable();
          message2_.add(builderForValue.build());
          onChanged();
        } else {
          message2Builder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder addMessage2(
          int index, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder builderForValue) {
        if (message2Builder_ == null) {
          ensureMessage2IsMutable();
          message2_.add(index, builderForValue.build());
          onChanged();
        } else {
          message2Builder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder addAllMessage2(
          java.lang.Iterable<? extends com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2> values) {
        if (message2Builder_ == null) {
          ensureMessage2IsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, message2_);
          onChanged();
        } else {
          message2Builder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder clearMessage2() {
        if (message2Builder_ == null) {
          message2_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          message2Builder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public Builder removeMessage2(int index) {
        if (message2Builder_ == null) {
          ensureMessage2IsMutable();
          message2_.remove(index);
          onChanged();
        } else {
          message2Builder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder getMessage2Builder(
          int index) {
        return getMessage2FieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder getMessage2OrBuilder(
          int index) {
        if (message2Builder_ == null) {
          return message2_.get(index);  } else {
          return message2Builder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public java.util.List<? extends com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder> 
           getMessage2OrBuilderList() {
        if (message2Builder_ != null) {
          return message2Builder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(message2_);
        }
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder addMessage2Builder() {
        return getMessage2FieldBuilder().addBuilder(
            com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.getDefaultInstance());
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder addMessage2Builder(
          int index) {
        return getMessage2FieldBuilder().addBuilder(
            index, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.getDefaultInstance());
      }
      /**
       * <code>repeated .com.pugwoo.weixin_msg.protobuf.SubMessage2 message2 = 3;</code>
       */
      public java.util.List<com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder> 
           getMessage2BuilderList() {
        return getMessage2FieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder> 
          getMessage2FieldBuilder() {
        if (message2Builder_ == null) {
          message2Builder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2.Builder, com.pugwoo.weixin_msg.protobuf.Msg.SubMessage2OrBuilder>(
                  message2_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          message2_ = null;
        }
        return message2Builder_;
      }

      // @@protoc_insertion_point(builder_scope:com.pugwoo.weixin_msg.protobuf.MessageBytesExtra)
    }

    // @@protoc_insertion_point(class_scope:com.pugwoo.weixin_msg.protobuf.MessageBytesExtra)
    private static final com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra();
    }

    public static com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MessageBytesExtra>
        PARSER = new com.google.protobuf.AbstractParser<MessageBytesExtra>() {
      @java.lang.Override
      public MessageBytesExtra parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MessageBytesExtra> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MessageBytesExtra> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.pugwoo.weixin_msg.protobuf.Msg.MessageBytesExtra getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tmsg.proto\022\036com.pugwoo.weixin_msg.proto" +
      "buf\"-\n\013SubMessage1\022\016\n\006field1\030\001 \001(\005\022\016\n\006fi" +
      "eld2\030\002 \001(\005\"-\n\013SubMessage2\022\016\n\006field1\030\001 \001(" +
      "\005\022\016\n\006field2\030\002 \001(\t\"\221\001\n\021MessageBytesExtra\022" +
      "=\n\010message1\030\001 \001(\0132+.com.pugwoo.weixin_ms" +
      "g.protobuf.SubMessage1\022=\n\010message2\030\003 \003(\013" +
      "2+.com.pugwoo.weixin_msg.protobuf.SubMes" +
      "sage2b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage1_descriptor,
        new java.lang.String[] { "Field1", "Field2", });
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_pugwoo_weixin_msg_protobuf_SubMessage2_descriptor,
        new java.lang.String[] { "Field1", "Field2", });
    internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_com_pugwoo_weixin_msg_protobuf_MessageBytesExtra_descriptor,
        new java.lang.String[] { "Message1", "Message2", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
