package com.pugwoo.weixin_msg.service;

import com.pugwoo.weixin_msg.config.GroupConfig;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.enums.SceneEnum;
import com.pugwoo.weixin_msg.utils.FileUtils;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ExportMedia {

    private static void plus(Map<SceneEnum, Integer> result, SceneEnum sceneEnum) {
        if (result.containsKey(sceneEnum)) {
            result.put(sceneEnum, result.get(sceneEnum) + 1);
        } else {
            result.put(sceneEnum, 1);
        }
    }

    @Data
    public static class ProcessResult {
        Map<SceneEnum, Integer> stats;
        List<Runnable> handles;
        int handleFileCount;
    }

    public static ProcessResult process(GroupConfig groupConfig) {
        Map<SceneEnum, Integer> result = new HashMap<>();
        result.put(SceneEnum.ERROR, 0);
        List<Runnable> handles = new ArrayList<>(); // 真正要处理的事情
        int handleFileCount = 0;

        // 每个群单独处理
        for (GroupConfig.GroupDTO group : groupConfig.getGroups()) {
            System.out.println("开始处理群：" + group.getName() + ", id:" + group.getId());
            List<MsgDTO> msgDTOS = MsgUtils.parseMsg(groupConfig.getDecryptMsgDir(),
                    group.getId(), true, group.getIgnoredMsgSvrIds());

            // 把图片另存为另外一个目录
            String groupBaseDir = groupConfig.getDestSaveDir() + "\\" + group.getName();

            List<String> existFiles = FileUtils.listFiles(groupBaseDir);
            Map<Long, String> existMsgIdToPath = FileNameService.getMsgIdToPath(existFiles);

            for (MsgDTO msg : msgDTOS) {
                boolean isFileNeedOperate = false;
                String exists = existMsgIdToPath.remove(msg.getMsgSvrID()); // 可能是原图也可能是缩略图

                if (msg.getFilePath() != null) { // 原消息有原图
                    String fullPath = FileNameService.getSavedFilePath(groupBaseDir, msg);
                    if (FileUtils.isExist(fullPath)) {
                        if (FileUtils.isSameFileQuickCheck(msg.getFilePath(), exists)) {
                            // 无需处理
                            plus(result, SceneEnum.SOURCE_ORIGIN_EXIST_ORIGIN);
                        } else {
                            throw new RuntimeException("原文件和目标文件都是原始文件，但内容不一致："
                               + fullPath + ";" + msg.getFilePath());
                        }
                        if (!Objects.equals(exists, fullPath)) {
                            throw new RuntimeException("当前已存在的该消息的文件" + exists + "和计算出来的文件" + fullPath + "不一致");
                        }
                    } else {
                        // 可能已经存在缩略图
                        if (StringTools.isNotBlank(exists)) {
                            if (!exists.contains("_thumb_")) {
                                plus(result, SceneEnum.SOURCE_ORIGIN_EXIST_ORIGIN_DUP_WARN);
                                handles.add(() -> {
                                    try {
                                        FileUtils.move(exists, exists + ".dup");
                                    } catch (Exception e) {
                                        plus(result, SceneEnum.ERROR);
                                    }
                                });
                                isFileNeedOperate = true;
                            } else {
                                plus(result, SceneEnum.SOURCE_ORIGIN_EXIST_THUMBNAIL);
                                handles.add(() -> {
                                    try {
                                        FileUtils.delete(exists);
                                        System.out.println("success delete file:" + exists);
                                    } catch (Exception e) {
                                        System.err.println("delete file error:" + exists);
                                        plus(result, SceneEnum.ERROR);
                                    }
                                });
                                isFileNeedOperate = true;
                            }
                        } else {
                            plus(result, SceneEnum.SOURCE_ORIGIN_EXIST_NO);
                        }
                        handles.add(() -> {
                            try {
                                FileUtils.copy(msg.getFilePath(), fullPath);
                                System.out.println("success copy file:" + msg.getFilePath() + " to " + fullPath);
                            } catch (Exception e) {
                                System.err.println("copy file error:" + msg.getFilePath() + " to " + fullPath);
                                plus(result, SceneEnum.ERROR);
                            }
                        });
                        isFileNeedOperate = true;
                    }
                } else if (msg.getThumbnailPath() != null) {
                    String fullPath = FileNameService.getSavedThumbnailPath(groupBaseDir, msg);
                    if (FileUtils.isExist(fullPath)) {
                        if (FileUtils.isSameFileQuickCheck(msg.getThumbnailPath(), fullPath)) {
                            plus(result, SceneEnum.SOURCE_THUMBNAIL_EXIST_THUMBNAIL);
                        } else {
                            throw new RuntimeException("原文件和目标文件都是缩略图，但内容不一致："
                                    + fullPath + ";" + msg.getThumbnailPath());
                        }
                        if (!Objects.equals(exists, fullPath)) {
                            throw new RuntimeException("当前已存在的该消息的文件" + exists + "和计算出来的文件" + fullPath + "不一致");
                        }
                    } else {
                        // 可能已经存在原图或缩略图
                        if (StringTools.isNotBlank(exists)) {
                            if (exists.contains("_thumb_")) {
                                plus(result, SceneEnum.SOURCE_THUMBNAIL_EXIST_THUMBNAIL_DUP_WARN);
                                handles.add(() -> {
                                    try {
                                        FileUtils.move(exists, exists + ".dup");
                                    } catch (Exception e) {
                                        plus(result, SceneEnum.ERROR);
                                    }
                                });
                                isFileNeedOperate = true;
                            } else {
                                plus(result, SceneEnum.SOURCE_THUMBNAIL_EXIST_ORIGIN);
                                // 不用处理，保留原图
                            }
                        } else {
                            plus(result, SceneEnum.SOURCE_THUMBNAIL_EXIST_NO);
                            handles.add(() -> {
                                try {
                                    FileUtils.copy(msg.getThumbnailPath(), fullPath);
                                    System.out.println("success copy thumbnail file:" + msg.getThumbnailPath() + " to " + fullPath);
                                } catch (Exception e) {
                                    System.err.println("copy file error:" + msg.getFilePath() + " to " + fullPath);
                                    plus(result, SceneEnum.ERROR);
                                }
                            });
                            isFileNeedOperate = true;
                        }
                    }
                } else {
                    System.err.println("file path or thumbnail path is null, msgSvrID=" + msg.getMsgSvrID()
                     + ", msg time:" + msg.getMsgTime());
                    plus(result, SceneEnum.SOURCE_ORIGIN_THUMBNAIL_NOT_EXIST);
                }

                if (isFileNeedOperate) {
                    handleFileCount++;
                }
            }

            // existMsgIdToPath 统计存在的
            for (Map.Entry<Long, String> entry : existMsgIdToPath.entrySet()) {
                String path = entry.getValue();
                if (path.contains("_thumb_")) {
                    plus(result, SceneEnum.SOURCE_NO_EXIST_THUMBNAIL);
                } else {
                    plus(result, SceneEnum.SOURCE_NO_EXIST_ORIGIN);
                }
            }
        }

        // 一并返回handles
        ProcessResult r = new ProcessResult();
        r.setStats(result);
        r.setHandles(handles);
        r.setHandleFileCount(handleFileCount);

        return r;
    }

}
