package com.pugwoo.weixin_msg.service;

import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 负责文件的命名

 * 2025年1月21日23:25:41  由于uuid会发生变化，故此处不加入uuid到文件名中了

 发现一个问题，对于相同的msgSvrId，多媒体的uuid会发生变化，再测试下是否所有类型都会发生变化：
 1）图片-缩略图：uuid变，文件内容大量不同，图片比较是一样的
 2）视频-原文件：uuid不变，文件后面补了16个0
 3) 视频-缩略图：uuid不变，缩略图不变
 4）图片-原图：uuid变，文件内容相差1个字节

 所以简单一点就是不要加入uuid了，另外文件大小相差小于256字节，都优先用原来的问题，即认为相等
 *
 */
public class FileNameService {

    /**
     * 获取最终保存起来的文件路径
     */
    public static String getSavedFilePath(String groupBaseDir, MsgDTO msg) {
        LocalDateTime msgTime = msg.getMsgTime();
        String yyyyMM = DateUtils.format(msgTime, "yyyy-MM");
        String time = DateUtils.format(msgTime, "yyyy-MM-dd_HHmmss");
        return groupBaseDir + "\\" + yyyyMM + "\\" + time + "_" +
                msg.getMsgSvrID() + (msg.getType() == MsgTypeEnum.VIDEO ? ".mp4" : ".jpg");
    }

    /**
     * 获取最终保存起来的文件路径
     */
    public static String getSavedThumbnailPath(String groupBaseDir, MsgDTO msg) {
        LocalDateTime msgTime = msg.getMsgTime();
        String yyyyMM = DateUtils.format(msgTime, "yyyy-MM");
        String time = DateUtils.format(msgTime, "yyyy-MM-dd_HHmmss");
        return groupBaseDir + "\\" + yyyyMM + "\\" + time + "_thumb_" +
                msg.getMsgSvrID() + (msg.getType() == MsgTypeEnum.VIDEO ? ".mp4.jpg" : ".jpg");
    }

    /**
     * 解析出消息id到文件路径的映射map
     */
    public static Map<Long, String> getMsgIdToPath(List<String> existFiles) {
        Map<Long, String> result = new HashMap<>();
        if (existFiles == null) {
            return result;
        }
        for (String existFile : existFiles) {
            // 去掉以.dup结尾的文件
            if (existFile.endsWith(".dup")) {
                continue;
            }

            int i = existFile.lastIndexOf("_");
            int e = existFile.indexOf(".", i);
            String msgId = existFile.substring(i + 1, e);
            if (StringTools.isBlank(msgId)) {
                throw new RuntimeException("获取不到消息id:" + existFile);
            }
            Long msgIdL = NumberUtils.parseLong(msgId);
            if (msgIdL == null) {
                throw new RuntimeException("获取不到消息id:" + existFile);
            }

            if (StringTools.isNotBlank(result.get(msgIdL))) {
                throw new RuntimeException("目标文件中，该消息id存在多个文件：" + msgIdL);
            }
            result.put(msgIdL, existFile);
        }
        return result;
    }

}
