## weixin-msg

一些决策结论：

1) 微信解密用https://github.com/git-jiadong/wechatDataBackup 太优秀了
2) 不依赖于mysql和其他db。

## 项目目录结构说明

1) src/main 用于正式的代码和打包后的命令行工具。
2) src/test 用于各种临时调试

## wechatDataBackup相关信息




## 正式部署使用

将项目打包成weixin-msg.jar，放到任意目录下，在该目录下新建application.yaml文件，根据需要配置，这里为示例：

```yaml
spring:
  application:
    name: weixin-msg

weixin:
  decryptMsgDir: E:\weixin-msg\User # 解密后的消息存放目录
  destSaveDir: E:\weiyun\sync\weixin-media # 保存的微信图片视频的目录
  groups: # 这个是要转换保存的群
    - name: 豆豆国际主题托管家庭群 
      id: 24507125117@chatroom
      ignoredMsgSvrIds:
        - 4135175694025292375

```
